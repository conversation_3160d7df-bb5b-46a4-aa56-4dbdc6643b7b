# FarmMaster 使用文档

欢迎使用 FarmMaster 加密货币自动化程序！

## 🚀 版本信息

**当前版本**：v1.7.3 (2025/7/15)

**最新更新**：
- 银河（Galxe）- 修复：：恢复领取积分功能，改进打码功能，现在跑项目不用打码了，优化日志显示内容，更精确显示日志
- 指纹浏览器 - 修复：：修复一次只能读取10个的问题
- 代理管理 - 新增功能：：增加序号显示

**待修复功能**：
- 代理设置：：增加Index

查看详细更新内容请访问 [更新日志](changelog/README.md)。

## 🌐 官方渠道

- **官方网站**：https://farmmaster.xyz/
- **官方Twitter**：https://x.com/FarmMaster_bot

## 📋 快速导航

### 📖 产品介绍
- [🏠 产品概述](#-核心功能) - FarmMaster功能概览
- [👥 关于我们](about/README.md) - 了解我们的团队和项目
- [📈 产品特色](about/README.md#产品特色) - 核心优势介绍

### ⚙️ 产品使用文档
- [💻 软件安装](installation/download.md) - 程序下载与激活
- [🔐 登录注册](installation/accounts.md) - 账号配置管理
- [🌐 环境管理](installation/proxy.md) - 网络代理配置
- [🖥️ 浏览器指纹管理](installation/fingerprint.md) - 指纹浏览器设置
- [� 任务管理](usage/README.md) - 任务配置与执行
- [🎯 项目交互](usage/project-interaction/README.md) - Web3项目自动化
- [⭐ 银河任务](usage/galxe/README.md) - Galxe平台任务
- [🛠️ 链上工具](usage/onchain-tools/README.md) - 区块链工具集
- [👤 RPA脚本](usage/fingerprint-browser/README.md) - 浏览器自动化
- [📊 数据管理](installation/config.md) - 配置参数说明

### 📋 帮助支持
- [❓ 常见问题](faq/README.md) - 问题解答和故障排除
- [📅 更新日志](changelog/README.md) - 版本更新记录

## 🚀 快速开始

如果您是第一次使用 FarmMaster，建议按以下顺序阅读：

1. **[关于我们](about/README.md)** - 了解项目背景
2. **[硬件配置推荐](installation/hardware.md)** - 检查系统要求
3. **[程序下载激活](installation/download.md)** - 下载并激活程序
4. **[各类账号配置](installation/accounts.md)** - 配置钱包和邮箱
5. **[项目交互](usage/project-interaction/README.md)** - 开始使用功能
6. **[常见问题](faq/README.md)** - 解决常见问题

## 💡 核心功能

### 项目交互
- **Irys项目**：自动领水、玩游戏、上传文件、Mint NFT
- **银河任务**：自动完成银河平台任务并领取奖励
- **多链支持**：支持EVM、Solana等多个区块链

### 自动化工具
- **指纹浏览器**：支持多账号管理
- **代理IP管理**：动态代理支持
- **打码服务**：在线和本地打码支持

## 🔧 技术支持

如果您在使用过程中遇到问题，请联系对应的技术支持人员：

- **项目交互问题**：@1号技师全心全意为您服务
- **银河/指纹浏览器问题**：@2号牛马在线解答
- **安装/更新/黑屏问题**：@孤风

## 🌐 在线文档

本文档同时支持：
- **GitBook版本**：完整的在线阅读体验
- **GitHub版本**：通过GitHub Pages访问

所有链接都已优化，确保在两个平台上都能正确显示。

## 📁 完整文档结构

查看完整的文档结构和站点地图：[📄 站点地图](sitemap.md)

## 👥 团队协作

### 🧑‍💻 开发者文档
- [📁 开发者文档中心](docs/developers/README.md) - 完整的开发者指南
- [📋 快速参考卡片](docs/developers/QUICK_REFERENCE.md) - 常用操作快速查询
- [📚 团队编辑指南](docs/developers/TEAM_EDITING_GUIDE.md) - 详细的编辑和维护流程
- [🤖 自动化指南](docs/developers/AUTOMATION_GUIDE.md) - GitHub Actions 自动化说明

### 🎯 适用人员
- **开发团队** - 添加新项目文档
- **产品团队** - 更新功能说明  
- **测试团队** - 补充常见问题
- **运营团队** - 维护版本日志