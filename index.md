---
title: FarmMaster 使用文档
description: FarmMaster 加密货币自动化程序使用文档
---

# FarmMaster 使用文档

欢迎使用 FarmMaster 加密货币自动化程序！

## 🚀 版本信息

**当前版本**：v1.7.3 (2025/7/15)

**最新更新**：
- 银河（Galxe）- 修复：：恢复领取积分功能，改进打码功能，现在跑项目不用打码了，优化日志显示内容，更精确显示日志
- 指纹浏览器 - 修复：：修复一次只能读取10个的问题
- 代理管理 - 新增功能：：增加序号显示

**待修复功能**：
- 代理设置：：增加Index

查看详细更新内容请访问 [更新日志](changelog/)。

## 🌐 官方渠道

- **官方网站**：https://farmmaster.xyz/
- **官方Twitter**：https://x.com/FarmMaster_bot

## 📋 完整目录

### 📖 基础信息
- [关于我们](about/) - 了解我们的团队和项目

### 🛠️ 安装配置教程
- [程序安装配置教程](installation/) - 完整的安装配置指南
  - [硬件配置推荐](installation/hardware.md)
  - [程序下载激活](installation/download.md)
  - [梯子下载使用](installation/proxy.md)
  - [代理IP选购配置](installation/proxy-ip.md)
  - [各类账号配置](installation/accounts.md)
  - [指纹浏览器配置](installation/fingerprint.md)
  - [打码配置](installation/captcha.md)
  - [程序常用配置解释](installation/config.md)

### 📚 使用教程
- [项目使用教程](usage/) - 详细的功能使用指南
  - [项目交互](usage/project-interaction/) - 区块链项目自动化交互
    - [Irys](usage/project-interaction/irys.md) - 去中心化数据存储链项目
  - [指纹浏览器项目](usage/fingerprint-browser/) - 指纹浏览器自动化管理
    - [AdsPower](usage/fingerprint-browser/adspower.md) - AdsPower 指纹浏览器
  - [银河交互](usage/galxe/) - 银河平台任务自动化
  - [链上工具](usage/onchain-tools/) - 区块链工具集合

### 📝 其他信息
- [更新日志](changelog/) - 版本更新记录和新功能
- [常见问题](faq/) - 常见问题解答 (0个问题)

## 🚀 快速开始

如果您是第一次使用 FarmMaster，建议按以下顺序阅读：

1. **[关于我们](about/)** - 了解项目背景
2. **[硬件配置推荐](installation/hardware.md)** - 检查系统要求
3. **[程序下载激活](installation/download.md)** - 下载并激活程序
4. **[各类账号配置](installation/accounts.md)** - 配置钱包和邮箱
5. **[项目交互](usage/project-interaction/)** - 开始使用功能
6. **[常见问题](faq/)** - 解决常见问题

## 💡 核心功能

### 项目交互
- **Irys项目**：自动领水、玩游戏、上传文件、Mint NFT
- **银河任务**：自动完成银河平台任务并领取奖励
- **多链支持**：支持EVM、Solana等多个区块链

### 自动化工具
- **指纹浏览器**：支持多账号管理
- **代理IP管理**：动态代理支持
- **打码服务**：在线和本地打码支持

## 🔧 技术支持

如果您在使用过程中遇到问题，请联系对应的技术支持人员：

- **项目交互问题**：@1号技师全心全意为您服务
- **银河/指纹浏览器问题**：@2号牛马在线解答
- **安装/更新/黑屏问题**：@孤风

## 🌐 文档格式

本文档提供两种格式：
- **GitBook版本**：支持GitBook的在线阅读体验
- **GitHub Pages版本**：通过GitHub Pages直接访问

所有链接都已优化，确保在两个平台上都能正确显示。