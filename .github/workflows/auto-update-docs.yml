name: 自动更新文档

on:
  push:
    branches: [ main ]
    paths:
      - 'changelog/README.md'
      - 'faq/README.md'
      - 'usage/*/**.md'
  
  # 允许手动触发
  workflow_dispatch:

permissions:
  contents: write
  issues: write
  pull-requests: write

jobs:
  update-docs:
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
        token: ${{ secrets.GITHUB_TOKEN }}
    
    - name: 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
    
    - name: 检查文件变更
      id: changes
      run: |
        # 检查哪些文件被修改
        if git diff --name-only HEAD~1 HEAD | grep -q "changelog/README.md"; then
          echo "changelog_changed=true" >> $GITHUB_OUTPUT
        fi
        if git diff --name-only HEAD~1 HEAD | grep -q "faq/README.md"; then
          echo "faq_changed=true" >> $GITHUB_OUTPUT
        fi
        if git diff --name-only HEAD~1 HEAD | grep -q "usage/.*\.md"; then
          echo "usage_changed=true" >> $GITHUB_OUTPUT
        fi
    
    - name: 更新版本信息
      if: steps.changes.outputs.changelog_changed == 'true'
      run: |
        node .github/scripts/update-version-info.js
    
    - name: 检查新项目文件
      if: steps.changes.outputs.usage_changed == 'true'
      run: |
        node .github/scripts/check-new-projects.js
    
    - name: 更新FAQ统计
      if: steps.changes.outputs.faq_changed == 'true'
      run: |
        node .github/scripts/update-faq-stats.js
    
    - name: 提交更改
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        
        # 检查是否有更改
        if [ -n "$(git status --porcelain)" ]; then
          git add .
          git commit -m "docs: 🤖 自动更新文档内容
          
          - 自动同步版本信息
          - 更新导航链接
          - 统计更新
          
          由 GitHub Actions 自动生成"
          git push
          echo "CHANGES_MADE=true" >> $GITHUB_ENV
        else
          echo "CHANGES_MADE=false" >> $GITHUB_ENV
        fi
    
    - name: 显示成功通知
      if: env.CHANGES_MADE == 'true'
      run: |
        echo "🎉 文档自动更新完成!"
        echo "✅ 检查文档变更"
        echo "✅ 更新版本信息"
        echo "✅ 同步导航链接"
        echo "✅ 提交更改到主分支"
        echo "📝 更新时间: $(date '+%Y-%m-%d %H:%M:%S')"
    
    - name: 显示失败通知
      if: failure()
      run: |
        echo "❌ 文档自动更新失败"
        echo "🔍 请检查以下可能的原因:"
        echo "  - 文档格式不符合解析要求"
        echo "  - 权限不足"
        echo "  - 网络连接问题"
        echo "📋 解决方案:"
        echo "  1. 检查最新的 changelog 格式是否正确"
        echo "  2. 手动运行工作流进行重试"
        echo "  3. 联系技术负责人处理"