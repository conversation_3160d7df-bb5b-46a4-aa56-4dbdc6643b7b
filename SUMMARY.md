# Summary

## 📖 产品介绍
* [🏠 产品概述](README.md)
* [👥 关于我们](about/README.md)
* [🔗 联系我们](about/README.md#联系我们)
* [📈 产品特色](about/README.md#产品特色)

## ⚙️ 产品使用文档
* [💻 1.T 软安装](installation/download.md)
* [🔐 2.登录注册](installation/accounts.md)
* [🌐 3.环境管理](installation/proxy.md)
* [🖥️ 4.浏览器指纹管理](installation/fingerprint.md)
* [🔧 5.任务管理](usage/README.md)
* [🎯 6.项目交互](usage/project-interaction/README.md)
* [⭐ 7.银河任务](usage/galxe/README.md)
* [🛠️ 8.链上工具](usage/onchain-tools/README.md)
* [👤 9.RPA脚本](usage/fingerprint-browser/README.md)
* [🤖 10.自动化脚本](usage/project-interaction/README.md)
* [🔍 DEFINE机制](installation/config.md)
* [📊 11.数据管理](installation/config.md)
* [💬 12.Discord管理](installation/accounts.md)
* [📧 13.邮箱管理](installation/accounts.md)
* [📱 14.Telegram管理](installation/accounts.md)
* [💰 15.钱包管理](installation/accounts.md)

## ❓ 常见问题
* [❓ 如何设置RPA脚本？](faq/README.md)
* [❓ 如何运行自动化脚本？](faq/README.md)
* [❓ 如何添加指纹Token环境？](faq/README.md)
* [❓ Telegram账号如何添加到项目？](faq/README.md)
* [❓ Telegram安全验证怎么操作？](faq/README.md)
* [📁 自动化脚本数据管理指南](usage/README.md)
* [❓ 关于机器码如何选择？](installation/hardware.md)
* [❓ PizzaChrome安全性如何保证？](installation/fingerprint.md)

## 🔧 高级功能
* [🔄 Vertus智能交易脚本](usage/project-interaction/README.md)
* [🦆 DuckChain智能交互脚本](usage/project-interaction/README.md)
* [📊 Tomarket 智能刷任务（APTOS）教程](usage/project-interaction/README.md)

## 📋 项目交互详情
* [💎 Irys 项目](usage/project-interaction/irys.md)
* [🌟 银河(Galxe)任务](usage/galxe/README.md)
* [🖱️ AdsPower 指纹浏览器](usage/fingerprint-browser/adspower.md)

## 📝 其他信息
* [📅 更新日志](changelog/README.md)
* [🆘 常见错误](errors/README.md)
* [🔧 硬件配置推荐](installation/hardware.md)
* [🌐 代理IP配置](installation/proxy-ip.md)
* [🔐 打码配置](installation/captcha.md)