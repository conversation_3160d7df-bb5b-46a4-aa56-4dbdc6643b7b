# Irys 项目交互

[← 返回项目交互](README.md) | [← 返回使用教程](../README.md) | [← 返回主页](../../README.md)

## 项目基本信息

Irys 是一个去中心化的 L1 可编程数据链，其核心在于数据存储与计算。目前已获得 890 万美元的融资，由 Framework Ventures 和 OpenSea 领投。

现阶段项目上线了几个链上小游戏，还有文件上传功能，交互基本免费大家可以尝试去交互一下。

**项目官网**：https://irysarcade.xyz/

## 🎯 核心功能

### 1. 自动领水（银河每日任务）
- 自动领水功能
- 支持数据已上传至银河，完成后可到银河页面领取奖励（可使用我们的银河功能批量领取）
- **银河链接**：https://app.galxe.com/quest/Irys/GCxxCtmLMP

### 2. 支持自动玩游戏（银河每日任务）
- 支持 贪吃蛇、青蛙过河、俄罗斯方块
- 支持数据已上传至银河，完成后可到银河页面领取奖励（可使用我们的银河功能批量领取）
- **银河链接**：https://app.galxe.com/quest/Irys/GC1mCtmb1N

### 3. 上传文件（银河每日任务）
- 自动生成文件并上传
- 支持数据已上传至银河，完成后可到银河页面领取奖励（可使用我们的银河功能批量领取）
- **银河链接**：https://app.galxe.com/quest/Irys/GCKHCtm2b8

### 4. Mint Bitomo NFT
- 支持 Bitomo NFT 铸造功能

## 📋 准备清单

完成全部任务需要以下准备：

| 任务类型 | 需要准备 |
|---------|----------|
| 领取测试币 | 代理IP（推荐动态），每个IP限领一次 |
| 玩游戏（每日任务） | EVM钱包 + 测试币 |
| 上传文件（每日任务） | Solana钱包 |
| MINT BITOMO NFT | 相关配置 |

## 🚀 操作流程

### 1. 钱包配置
1. 点击运行配置板块的「下载模板」按钮
2. 按模板补充钱包地址、私钥等内容
3. 配置完毕后保存表格
4. 点击「选择文件」按钮上传钱包数据文件

### 2. 运行配置
参考 [程序常用配置解释](../../installation/config.md) → 项目交互页面的字段配置解释

### 3. 任务选择配置
勾选需要执行的任务即可

### 4. 启动任务
点击「启动任务」程序即可运行

## 💡 操作技巧

### 领取测试币
- 推荐使用动态代理
- 每天跑一轮（不用管成功还是失败）
- 跑一周基本都有币了
- 跑完再到银河页面跑一次领取奖励

### 玩游戏
- 每天跑一次，每次跑一个游戏即可
- 跑完再到银河页面跑一次领取奖励

### 上传文件
- 不需要测试币，可以直接开跑
- 每天跑一次，跑完再到银河页面跑一次领取奖励
- 银河帐户需要先绑定好对应的Solana钱包

## ⚠️ 注意事项

- 邀请码格式为：xxxxxxx
- 代理配置和技巧请看 [代理IP配置](../../installation/proxy-ip.md) 页面

## ❓ 常见问题

### Q：玩游戏玩哪个合适？
**A：** 游戏都是一样的，随便选一个就行。

### Q：交互时报错 read ECONNRESET 是什么原因？
**A：** 包含read ECONNRESET的错误一般是网络原因，请检查一下代理/梯子是否稳定。

## 🔧 技术支持

如果遇到 Irys 项目相关问题，请联系：@1号技师全心全意为您服务