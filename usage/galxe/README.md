# 银河交互

[← 返回使用教程](../README.md) | [← 返回主页](../../README.md)

银河（Galxe）交互模块支持自动化完成银河平台的各种任务和奖励领取。

## 🎯 核心功能

### 自动账户管理

-   自动注册银河账户
-   绑定EVM钱包
-   绑定Solana钱包
-   绑定推特账户
-   绑定邮箱账户（支持IMAP及mail3.me）
-   自动充值至Smart Savings
-   自动开通Pulse会员

### 自动任务完成

-   支持多种任务类型自动完成：
    -   推特任务：关注、点赞、转发、@好友
    -   日常任务：访问链接、观看YouTube视频
    -   调研任务：单选/多选、问卷
    -   平台任务：关注银河、任务验证
-   智能识别任务状态，自动跳过已完成任务
-   支持批量处理多个账户

### 奖励领取管理

-   自动检测可领取的奖励
-   支持批量领取OAT和积分
-   支持Pulse会员免Gas领取奖励
-   详细的奖励领取记录与统计

### 🆕 即将支持

-   **批量任务执行**：一次性执行多个任务链接
-   **银河任务抓取**：从指定项目空间抓取所有任务
-   **任务上新监控**：自动监控项目更新并推送通知

## 📋 准备清单

使用银河交互功能需要以下准备：

| 项目 | 说明 |
| :--- | :--- |
| **EVM钱包** | 用于登录和交互的钱包，程序会自动注册或登录。 |
| **邮箱账户** | 用于绑定账户，推荐使用`ru`后缀的邮箱。 |
| **Twitter账户** | 用于完成关注、点赞、转推等推特任务。 |
| **代理IP** | 推荐为每个账户配置独立的代理，以确保网络稳定。 |
| **任务链接** | 需要自动完成的Galxe任务URL。 |
| **其他** | 根据具体任务要求，可能需要Solana/Move钱包等。 |

## 🚀 操作流程

### 1. 账户配置

1.  在**钱包配置**板块，点击右上角的**下载模板**，按照提示填写账户信息后上传。
2.  参考[**代理IP选购配置**](../../installation/proxy-ip.md)文档，为账户配置代理IP（可选但推荐）。

### 2. 任务配置

1.  **选择交互动作**：
    -   **注册&绑定**：执行账户注册、绑定钱包/邮箱、开通会员等操作。
    -   **自动完成任务**：根据任务链接，自动执行并验证任务。
2.  **设置任务参数**：
    -   指定地址序号、交互动作、线程数等。
    -   若为自动执行，输入任务链接，点击**获取任务**，并勾选要执行的具体任务。
3.  **选择执行选项**：
    -   可以仅完成任务、仅领取奖励，或两者都执行。

### 3. 启动任务

点击**启动任务**，程序将开始自动化操作。

## 💡 操作技巧

### 账户管理

-   **独立代理**：为每个账户配置独立的代理IP，避免关联风险。
-   **定期检查**：定期检查账户的绑定状态，确保所有功能正常。
-   **异常处理**：及时处理执行日志中报告的异常账户。

### 任务执行

-   **分批操作**：建议分批次处理大量任务，避免因操作频率过高而被平台限制。
-   **监控日志**：密切关注任务执行日志，了解每个步骤的执行情况。
-   **参数调整**：根据平台规则和网络状况，灵活调整线程数等参数。

## ⚠️ 注意事项

-   确保用于交互的账户已正确绑定钱包和社交媒体账户。
-   请遵守Galxe平台的使用规则，避免滥用自动化工具。
-   注意任务的执行频率，过于频繁的操作可能导致临时封禁。
-   定期检查并领取已完成任务的奖励。

## ❓ 常见问题

### Q：银河账户连接失败怎么办？

**A：** 首先检查账户是否能正常登录Galxe官网，然后确认代理网络是否稳定。

### Q：任务无法自动完成？

**A：** 请检查任务链接是否正确，并确认该任务类型是否在支持列表中。部分特殊任务可能需要手动完成。

### Q：奖励领取失败？

**A：** 检查钱包状态，确保网络连接正常，并满足任务发起方设置的领取条件。

## 🔧 技术支持

如遇银河交互相关问题，请联系技术支持：**@2号牛马在线解答**