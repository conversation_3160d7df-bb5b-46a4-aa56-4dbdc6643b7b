# 项目使用教程

[← 返回主页](../README.md)

本章节详细介绍各个功能模块的使用方法和操作流程。

## 📋 功能模块

### 🔗 项目交互
- [项目交互](project-interaction/README.md) - 区块链项目自动化交互
  - [Irys](project-interaction/irys.md) - 去中心化数据存储链项目

### 🌐 指纹浏览器
- [指纹浏览器项目](fingerprint-browser/README.md) - 指纹浏览器自动化管理
  - [AdsPower](fingerprint-browser/adspower.md) - AdsPower 指纹浏览器

### 🎯 银河交互
- [银河交互](galxe/README.md) - 银河平台任务自动化

### 🔧 链上工具
- [链上工具](onchain-tools/README.md) - 区块链工具集合

## 🚀 快速开始

在使用前，请确保您已经完成了 [程序安装配置教程](../installation/README.md)。

### 新手推荐流程

1. **[项目交互](project-interaction/README.md)** - 从简单的项目交互开始
2. **[银河交互](galxe/README.md)** - 学习银河平台功能
3. **[指纹浏览器项目](fingerprint-browser/README.md)** - 高级账号管理
4. **[链上工具](onchain-tools/README.md)** - 使用专业工具

### 按需选择

根据您的需求选择对应的功能模块：

| 需求 | 推荐模块 |
|-----|---------|
| 区块链项目交互 | [项目交互](project-interaction/README.md) |
| 多账号管理 | [指纹浏览器项目](fingerprint-browser/README.md) |
| 银河任务自动化 | [银河交互](galxe/README.md) |
| 交易所操作 | [链上工具](onchain-tools/README.md) |

## 💡 使用技巧

### 安全建议
- 使用专用钱包进行操作
- 定期备份重要数据
- 合理设置代理IP

### 效率优化
- 分批次处理任务
- 监控执行日志
- 及时调整参数

## ⚠️ 注意事项

- 确保网络连接稳定
- 遵守各平台使用规则
- 注意资产安全
- 定期检查任务状态

## 🔧 技术支持

如果遇到问题，请查看 [常见问题](../faq/README.md) 或联系对应的技术支持：

- **项目交互问题**：@1号技师全心全意为您服务
- **银河/指纹浏览器问题**：@2号牛马在线解答
- **安装/更新/黑屏问题**：@孤风