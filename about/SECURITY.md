# 如何保护私钥安全

## 1. FarmmMaster 的运作机制

FarmMaster所有任务脚本依赖您本机的钱包私钥模板文档，通过 fs 模块读取文件并传给脚本进行执行，全程没有任何上传到云端的行为，所有数据都保存在您的本地设备上，如您拥有一定的技术能力，可以使用抓包程序“fiddler”等工具进行请求抓包分析。

## 2. 保护私钥安全的建议

1. 由于模板文件并未进行数据加密，所有私钥都是明文储存，请不要在常用的设备上运行 FarmMaster，特别是常用来工作和游戏的 windows 电脑
2. 使用 FarmMaster 的设备一定要保证使用环境安全，无不明数据，日常不做任何不明来源的软件下载，不访问任何不明网站，专机专用。我们建议可以购置一台二手 macmini 主机
3. 如果不愿购置新的设备且有一定技术能力，可以将 FarmMaster 安装在专用的虚拟机中运行，这样可以有效隔离您的常用设备和 FarmMaster 的运行环境