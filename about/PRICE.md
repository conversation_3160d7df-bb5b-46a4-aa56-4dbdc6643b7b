# 💰 定价方案

[← 返回主页](../README.md)

选择最适合您需求的FarmMaster版本，开启高效的Web3自动化之旅。

---

## 🎯 版本对比

<div style="display: flex; gap: 20px; margin: 30px 0; flex-wrap: wrap;">

<!-- 基础版卡片 -->
<div style="flex: 1; min-width: 300px; border: 2px solid #e1e5e9; border-radius: 12px; padding: 24px; background: #f8f9fa;">
  <div style="text-align: center; margin-bottom: 20px;">
    <h3 style="color: #0366d6; margin: 0; font-size: 24px;">🚀 基础版</h3>
    <p style="color: #586069; margin: 8px 0 0 0;">入门首选</p>
  </div>

  <div style="margin: 20px 0;">
    <h4 style="color: #24292e; margin: 0 0 12px 0;">功能限制</h4>
    <ul style="margin: 0; padding-left: 20px; color: #586069;">
      <li>协议交互：<strong>200个钱包</strong>/项目</li>
      <li>其他功能：<strong>无限制</strong></li>
    </ul>
  </div>

  <div style="margin: 20px 0;">
    <h4 style="color: #24292e; margin: 0 0 12px 0;">定价</h4>
    <div style="margin: 8px 0;">
      <span style="background: #f1f8ff; padding: 4px 8px; border-radius: 6px; color: #0366d6; font-weight: bold;">月卡 98U</span>
    </div>
    <div style="margin: 8px 0;">
      <span style="background: #f1f8ff; padding: 4px 8px; border-radius: 6px; color: #0366d6; font-weight: bold;">季卡 294U</span>
      <span style="color: #28a745; font-size: 12px; margin-left: 8px;">+15天</span>
    </div>
    <div style="margin: 8px 0;">
      <span style="background: #f1f8ff; padding: 4px 8px; border-radius: 6px; color: #0366d6; font-weight: bold;">半年卡 588U</span>
      <span style="color: #28a745; font-size: 12px; margin-left: 8px;">+1个月</span>
    </div>
  </div>
</div>

<!-- 进阶版卡片 -->
<div style="flex: 1; min-width: 300px; border: 2px solid #28a745; border-radius: 12px; padding: 24px; background: #f6ffed; position: relative;">
  <div style="position: absolute; top: -10px; right: 20px; background: #28a745; color: white; padding: 4px 12px; border-radius: 12px; font-size: 12px; font-weight: bold;">推荐</div>

  <div style="text-align: center; margin-bottom: 20px;">
    <h3 style="color: #28a745; margin: 0; font-size: 24px;">⭐ 进阶版</h3>
    <p style="color: #586069; margin: 8px 0 0 0;">性价比之选</p>
  </div>

  <div style="margin: 20px 0;">
    <h4 style="color: #24292e; margin: 0 0 12px 0;">功能限制</h4>
    <ul style="margin: 0; padding-left: 20px; color: #586069;">
      <li>协议交互：<strong>1000个钱包</strong>/项目</li>
      <li>其他功能：<strong>无限制</strong></li>
    </ul>
  </div>

  <div style="margin: 20px 0;">
    <h4 style="color: #24292e; margin: 0 0 12px 0;">定价</h4>
    <div style="margin: 8px 0;">
      <span style="background: #e6ffed; padding: 4px 8px; border-radius: 6px; color: #28a745; font-weight: bold;">月卡 148U</span>
    </div>
    <div style="margin: 8px 0;">
      <span style="background: #e6ffed; padding: 4px 8px; border-radius: 6px; color: #28a745; font-weight: bold;">季卡 444U</span>
      <span style="color: #28a745; font-size: 12px; margin-left: 8px;">+15天</span>
    </div>
    <div style="margin: 8px 0;">
      <span style="background: #e6ffed; padding: 4px 8px; border-radius: 6px; color: #28a745; font-weight: bold;">半年卡 888U</span>
      <span style="color: #28a745; font-size: 12px; margin-left: 8px;">+1个月</span>
    </div>
  </div>
</div>

<!-- 无限版卡片 -->
<div style="flex: 1; min-width: 300px; border: 2px solid #6f42c1; border-radius: 12px; padding: 24px; background: #f8f5ff;">
  <div style="text-align: center; margin-bottom: 20px;">
    <h3 style="color: #6f42c1; margin: 0; font-size: 24px;">👑 无限版</h3>
    <p style="color: #586069; margin: 8px 0 0 0;">专业用户</p>
  </div>

  <div style="margin: 20px 0;">
    <h4 style="color: #24292e; margin: 0 0 12px 0;">功能限制</h4>
    <ul style="margin: 0; padding-left: 20px; color: #586069;">
      <li>协议交互：<strong>无限制</strong></li>
      <li>其他功能：<strong>无限制</strong></li>
    </ul>
  </div>

  <div style="margin: 20px 0;">
    <h4 style="color: #24292e; margin: 0 0 12px 0;">定价</h4>
    <div style="margin: 8px 0;">
      <span style="background: #f3f0ff; padding: 4px 8px; border-radius: 6px; color: #6f42c1; font-weight: bold;">月卡 228U</span>
    </div>
    <div style="margin: 8px 0;">
      <span style="background: #f3f0ff; padding: 4px 8px; border-radius: 6px; color: #6f42c1; font-weight: bold;">季卡 684U</span>
      <span style="color: #28a745; font-size: 12px; margin-left: 8px;">+15天</span>
    </div>
    <div style="margin: 8px 0;">
      <span style="background: #f3f0ff; padding: 4px 8px; border-radius: 6px; color: #6f42c1; font-weight: bold;">半年卡 1368U</span>
      <span style="color: #28a745; font-size: 12px; margin-left: 8px;">+1个月</span>
    </div>
  </div>

  <div style="margin: 20px 0;">
    <h4 style="color: #24292e; margin: 0 0 12px 0;">🎁 专属福利</h4>
    <ul style="margin: 0; padding-left: 20px; color: #586069; font-size: 14px;">
      <li><strong>季卡</strong>：赠送1个专属项目定制</li>
      <li><strong>半年卡</strong>：赠送3个专属项目定制</li>
    </ul>
    <p style="font-size: 12px; color: #6a737d; margin: 8px 0 0 0;">*提供项目及策略，交付源代码</p>
  </div>
</div>

</div>

---

## 🎁 购买福利

### 时长赠送
| 购买周期 | 赠送时长 | 说明 |
|---------|---------|------|
| 月卡 | 无赠送 | 标准时长 |
| 季卡 | +15天 | 额外半个月 |
| 半年卡 | +1个月 | 额外一个月 |

### 无限版专属定制
- **季卡用户**：获得1个专属项目定制机会
- **半年卡用户**：获得3个专属项目定制机会

**定制说明**：
- 由您提供项目信息和交互策略
- 我们开发对应的自动化脚本
- 完成后交付完整源代码
- 享受后续技术支持

---

## 💡 选择建议

### 🚀 基础版适合
- 初次接触Web3自动化
- 钱包数量较少（<200个）
- 预算有限的个人用户

### ⭐ 进阶版适合
- 有一定经验的用户
- 中等规模操作（200-1000个钱包）
- 追求性价比的用户

### 👑 无限版适合
- 专业批量操作用户
- 大规模钱包管理需求
- 需要定制化开发的用户

---

## 📞 购买咨询

如需购买或了解更多信息，请联系我们：

- **官方网站**：https://farmmaster.xyz/
- **Twitter**：https://x.com/FarmMaster_bot
- **技术支持**：@孤风

---

💡 **提示**：建议首次用户从基础版开始体验，后续可根据需求升级到更高版本。