# 🔐 数据安全

[← 返回主页](../README.md)

保护您的私钥和数据安全是使用FarmMaster的重要前提。本页面详细说明程序的安全机制和最佳实践建议。

## 🛡️ FarmMaster 安全机制

### 本地化运行
FarmMaster所有任务脚本依赖您本机的钱包私钥模板文档，通过 fs 模块读取文件并传给脚本进行执行，**全程没有任何上传到云端的行为**，所有数据都保存在您的本地设备上。

### 透明验证
如您拥有一定的技术能力，可以使用抓包程序"Fiddler"等工具进行请求抓包分析，验证程序的网络行为。

## ⚠️ 安全风险提示

### 🔴 明文存储风险
由于模板文件并未进行数据加密，所有私钥都是明文储存，因此设备安全至关重要。

### 🔴 高风险环境
- ❌ 常用的工作和游戏设备（特别是Windows电脑）
- ❌ 网络环境复杂的设备
- ❌ 安装了来源不明软件的设备

## 🛡️ 安全防护建议

### 🥇 最佳方案：专用设备
**推荐配置**：购置一台二手 Mac mini 主机或专用设备
- ✅ 专机专用，仅用于FarmMaster运行
- ✅ 保证使用环境安全，无不明数据
- ✅ 日常不做任何不明来源的软件下载
- ✅ 不访问任何不明网站

### 🥈 次选方案：虚拟机隔离
如果不愿购置新设备且有一定技术能力：
- ✅ 将FarmMaster安装在专用的虚拟机中运行
- ✅ 有效隔离常用设备和FarmMaster的运行环境
- ✅ 独立的网络和存储环境

### 🥉 基础方案：环境清理
如果必须在现有设备使用：
- ⚠️ 彻底清理设备，移除不必要软件
- ⚠️ 确保系统安全更新
- ⚠️ 使用可靠的安全软件

## 🔒 私钥管理最佳实践

### 文件安全
- **存储位置**：选择安全的本地目录
- **访问权限**：设置适当的文件访问权限
- **备份策略**：重要私钥文件加密备份
- **定期清理**：及时清理不再使用的私钥文件

### 使用规范
- **最小权限**：仅导入必要的私钥信息
- **分批管理**：避免在单个文件中存储过多私钥
- **测试验证**：使用小额资金进行功能测试
- **监控异常**：定期检查钱包余额和交易记录

## 🚨 应急处理

### 发现异常时
1. **立即停止**：停止所有正在运行的任务
2. **断网隔离**：断开设备网络连接
3. **资产转移**：将重要资产转移到安全钱包
4. **环境检查**：全面检查设备安全状态
5. **联系支持**：及时联系技术支持团队

## 📞 安全支持

如果您在使用过程中遇到安全相关问题：

- **安装/更新/安全问题**：@孤风
- **官方渠道**：https://farmmaster.xyz/
- **Twitter**：https://x.com/FarmMaster_bot

---

💡 **重要提醒**：数字资产安全无小事，请务必按照安全建议操作，确保您的资产安全。
