# 网络代理配置

[← 返回安装教程](README.md) | [← 返回主页](../README.md)

## 重要说明

由于绝大部分 Web3 项目的API接口都需要使用 VPN 才能访问，同时考虑到每一位用户的本地网络环境各有千秋，所以以下是做了参考的 VPN 软件使用已经配置指南

⚠️ **注意**: 为确保网络代理 IP 稳定可靠，建议选择月费超过 20元 的付费机器，避免使用免费或低价的不稳定的机场

## 推荐代理软件

### 桌面端软件
- **ClashX** - 功能强大，支持多种协议
- **Shadowsocks** - 轻量级，简单易用
- **快连** - 简单易用，适合新手

⚠️ **注意**: 此处仅为列举市面上常见的工具，自行选择适合自己的即可

## Clash 配置指南

### 1. 下载安装
- **Windows**: 建议使用Clash for Windows
- **macOS**: 建议使用ClashX

### 2. 基本配置
1. **导入订阅**
   - 从服务商获取订阅链接
   - 在 Clash 中导入配置文件
   - 点击 "更新" 获取最新节点

2. **选择节点**
   - 在 "代理" 选项卡中选择合适的节点
   - 推荐选择香港、日本、新加坡节点
   - 避免选择欧美节点（延迟高，可能被限制）

3. **启用代理**
   - 打开 "系统代理" 开关
   - 启用 "TUN 模式"（推荐）
   - 选择 "全局模式" 或 "规则模式"


## 快连配置指南

### 1. 简单设置
1. **下载安装**
   - 从官网下载快连客户端
   - 注册账户并登录

2. **选择节点**
   - 在节点列表中选择合适的服务器
   - 点击 ‘开启快连’ 按钮

3. **模式设置**
   - 开启 "安全模式"，约等于其他软件的全局代理


## 推荐配置设置

### 通用设置
无论使用哪种代理软件，都建议进行以下设置：

1. **启用 TUN 模式**
   - 提供更好的网络接管能力
   - 减少应用程序兼容性问题
   - 提高网络稳定性

2. **启用增强模式**
   - 更好的流量处理能力
   - 支持更多网络协议
   - 提升连接稳定性

3. **开启全局代理**
   - 确保所有网络流量都通过代理
   - 避免部分应用绕过代理

## 网络优化建议

### 节点选择
1. **地理位置**
   - 优先选择香港、日本、新加坡、韩国节点
   - 避免选择欧美节点，特别是美国

2. **延迟测试**
   - 使用软件内置的延迟测试功能
   - 选择延迟低于 300ms 的节点
   - 定期测试并更换节点

### 网络稳定性
1. **定期更新**
   - 及时更新代理软件版本
   - 定期更新订阅配置
   - 关注服务商的通知

## 常见问题解决

### 连接问题
- **无法连接**: 检查服务商服务状态，尝试更换节点
- **连接不稳定**: 更换协议或节点，检查本地网络
- **速度慢**: 选择延迟更低的节点，检查带宽限制

### 软件问题
- **无法启动**: 以管理员身份运行，检查防火墙设置
- **系统代理失效**: 重启软件，重新配置系统代理

### FarmMaster 连接问题
- **API 调用失败**: 确保代理正常工作，检查节点稳定性
- **登录失败**: 尝试更换不同地区的节点
- **功能异常**: 检查是否使用了全局代理模式


## 技术支持

如果在配置过程中遇到问题：
1. 查看代理软件的官方文档
2. 联系代理服务商的客服支持
3. 参考我们的 [网络问题排除指南](../docs/network-troubleshooting.md)
4. 联系 FarmMaster 技术支持获取帮助