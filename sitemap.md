# 站点地图

## 📁 文档结构

```
📁 FarmMaster 使用文档
├── 📄 README.md (主页)
├── 📄 SUMMARY.md (GitBook目录)
├── 📄 index.md (GitHub Pages入口)
├── 📄 sitemap.md (本页面)
├── 📁 about/ (关于我们)
│   └── 📄 README.md
├── 📁 installation/ (安装配置教程)
│   ├── 📄 README.md
│   ├── 📄 hardware.md (硬件配置推荐)
│   ├── 📄 download.md (程序下载激活)
│   ├── 📄 proxy.md (梯子下载使用)
│   ├── 📄 proxy-ip.md (代理IP选购配置)
│   ├── 📄 accounts.md (各类账号配置)
│   ├── 📄 fingerprint.md (指纹浏览器配置)
│   ├── 📄 captcha.md (打码配置)
│   └── 📄 config.md (程序常用配置解释)
├── 📁 usage/ (使用教程)
│   ├── 📄 README.md
│   ├── 📁 project-interaction/ (项目交互)
│   │   ├── 📄 README.md
│   │   └── 📄 irys.md (<PERSON><PERSON>s项目)
│   ├── 📁 fingerprint-browser/ (指纹浏览器项目)
│   │   ├── 📄 README.md
│   │   └── 📄 adspower.md (AdsPower)
│   ├── 📁 galxe/ (银河交互)
│   │   └── 📄 README.md
│   └── 📁 onchain-tools/ (链上工具)
│       └── 📄 README.md
├── 📁 changelog/ (更新日志)
│   └── 📄 README.md
├── 📁 faq/ (常见问题)
│   └── 📄 README.md
└── 📁 docs/ (开发者文档)
    └── 📁 developers/
        ├── 📄 README.md (开发者文档中心)
        ├── 📄 QUICK_REFERENCE.md (快速参考卡片)
        ├── 📄 TEAM_EDITING_GUIDE.md (团队编辑指南)
        └── 📄 AUTOMATION_GUIDE.md (自动化指南)
```

## 🔗 快速链接

### 新用户指南
1. [关于我们](about/README.md)
2. [硬件配置推荐](installation/hardware.md)
3. [程序下载激活](installation/download.md)
4. [各类账号配置](installation/accounts.md)
5. [项目交互](usage/project-interaction/README.md)

### 常用功能
- [项目交互教程](usage/project-interaction/README.md)
- [Irys项目交互](usage/project-interaction/irys.md)
- [指纹浏览器项目](usage/fingerprint-browser/README.md)
- [AdsPower指纹浏览器](usage/fingerprint-browser/adspower.md)
- [银河交互功能](usage/galxe/README.md)
- [链上工具使用](usage/onchain-tools/README.md)

### 技术支持
- [常见问题解答](faq/README.md)
- [更新日志](changelog/README.md)

### 开发者文档
- [开发者文档中心](docs/developers/README.md)
- [快速参考卡片](docs/developers/QUICK_REFERENCE.md)
- [团队编辑指南](docs/developers/TEAM_EDITING_GUIDE.md)
- [自动化指南](docs/developers/AUTOMATION_GUIDE.md)

## 📱 多平台支持

本文档支持以下平台：
- **GitBook**: 使用 `SUMMARY.md` 文件定义的目录结构
- **GitHub Pages**: 使用 `index.md` 作为入口页面
- **GitHub**: 使用 `README.md` 作为默认显示页面

每个页面都包含完整的导航链接，确保在任何平台上都能正常浏览。