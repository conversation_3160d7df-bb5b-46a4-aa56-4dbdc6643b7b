# 团队文档编辑指南

> 🎯 **目标**：帮助团队成员快速掌握文档编辑和维护流程

## 📋 目录

- [文档结构说明](#文档结构说明)
- [新项目添加流程](#新项目添加流程)
- [常见编辑任务](#常见编辑任务)
- [文档发布流程](#文档发布流程)
- [编辑规范](#编辑规范)
- [常见问题](#常见问题)

---

## 📁 文档结构说明

### 总体架构

```
📁 FarmMaster 根目录
├── 📄 README.md              # 主页，包含完整导航
├── 📄 SUMMARY.md             # GitBook目录结构
├── 📄 index.md               # GitHub Pages入口
├── 📄 sitemap.md             # 站点地图
├── 📄 _config.yml            # Jekyll配置
├── 📄 TEAM_EDITING_GUIDE.md  # 本文档
├── 📁 about/                 # 关于我们
├── 📁 installation/          # 安装配置教程
├── 📁 usage/                 # 使用教程 (重点)
├── 📁 changelog/             # 更新日志
└── 📁 faq/                   # 常见问题
```

### 使用教程结构 (usage/)

```
📁 usage/
├── 📄 README.md                          # 使用教程主页
├── 📁 project-interaction/               # 项目交互
│   ├── 📄 README.md                      # 项目交互主页
│   ├── 📄 irys.md                        # Irys项目
│   └── 📄 [新项目].md                    # 新项目文件
├── 📁 fingerprint-browser/               # 指纹浏览器项目
│   ├── 📄 README.md                      # 指纹浏览器主页
│   ├── 📄 adspower.md                    # AdsPower
│   └── 📄 [新浏览器].md                  # 新浏览器文件
├── 📁 galxe/                             # 银河交互
│   ├── 📄 README.md                      # 银河交互主页
│   └── 📄 [新功能].md                    # 新功能文件
└── 📁 onchain-tools/                     # 链上工具
    ├── 📄 README.md                      # 链上工具主页
    └── 📄 [新工具].md                    # 新工具文件
```

---

## 🆕 新项目添加流程

### 1. 确定项目类别

首先确定新项目属于哪个类别：

| 类别 | 目录 | 适用项目 |
|------|------|----------|
| 项目交互 | `usage/project-interaction/` | 区块链项目、DeFi协议等 |
| 指纹浏览器 | `usage/fingerprint-browser/` | 各种指纹浏览器工具 |
| 银河交互 | `usage/galxe/` | 银河平台相关功能 |
| 链上工具 | `usage/onchain-tools/` | 交易所、钱包、工具等 |

### 2. 创建项目文件

#### 步骤 1：创建项目文件
```bash
# 以添加新项目 "Warden" 到项目交互为例
cd usage/project-interaction/
touch warden.md
```

#### 步骤 2：使用项目模板
复制以下模板到新文件：

```markdown
# [项目名称] 项目交互

[← 返回项目交互](README.md) | [← 返回使用教程](../README.md) | [← 返回主页](../../README.md)

## 项目基本信息

[项目简介，包括背景、融资情况等]

**项目官网**：[项目网址]

## 🎯 核心功能

### 1. 功能一
- 功能描述
- 相关说明

### 2. 功能二
- 功能描述
- 相关说明

## 📋 准备清单

使用本项目功能需要以下准备：

| 项目 | 需要准备 |
|-----|----------|
| 项目1 | 准备说明 |
| 项目2 | 准备说明 |

## 🚀 操作流程

### 1. 配置步骤
1. 详细步骤说明
2. 配置要点
3. 注意事项

### 2. 任务执行
1. 执行步骤
2. 参数设置
3. 启动任务

## 💡 操作技巧

### 技巧一
- 具体建议
- 最佳实践

### 技巧二
- 具体建议
- 最佳实践

## ⚠️ 注意事项

- 重要提醒1
- 重要提醒2
- 重要提醒3

## ❓ 常见问题

### Q：问题1？
**A：** 解答1

### Q：问题2？
**A：** 解答2

## 🔧 技术支持

[项目名称] 相关问题请联系：@对应技术支持人员
```

### 3. 更新导航文件

#### 步骤 1：更新类别主页
在对应类别的 `README.md` 中添加新项目链接：

```markdown
# 示例：更新 usage/project-interaction/README.md

### 🔗 主流项目
- [Irys](irys.md) - 去中心化数据存储链项目
- [Warden](warden.md) - 新添加的项目  # 新增这一行
```

#### 步骤 2：更新 SUMMARY.md
在 `SUMMARY.md` 中添加新项目：

```markdown
* [项目交互](usage/project-interaction/README.md)
  * [Irys](usage/project-interaction/irys.md)
  * [Warden](usage/project-interaction/warden.md)  # 新增这一行
```

#### 步骤 3：更新主页和 index.md
在 `README.md` 和 `index.md` 中的目录部分添加新项目链接。

#### 步骤 4：更新站点地图
在 `sitemap.md` 中添加新项目到对应的文档结构和快速链接部分。

---

## 📝 常见编辑任务

### 1. 更新项目信息

#### 更新现有项目
1. 找到对应的项目文件（如 `usage/project-interaction/irys.md`）
2. 直接编辑内容
3. 保存文件

#### 常见更新内容
- 项目网址变更
- 新功能添加
- 操作流程调整
- 常见问题更新

### 2. 添加新功能到现有项目

在现有项目文件中添加新功能：

```markdown
### 3. 新功能名称
- 功能描述
- 使用方法
- 注意事项
```

### 3. 更新版本信息

#### 更新日志 (changelog/README.md)
```markdown
## 版本 vX.X.X (日期)

### 更新内容
1. **新增功能**：
   - 具体功能描述
   - 相关说明

2. **功能优化**：
   - 优化内容
   - 改进说明

### 修复问题
- 修复的问题描述
```

### 4. 更新常见问题

在 `faq/README.md` 中添加新问题：

```markdown
**问：新问题描述？**
答：详细解答内容。
```

---

## 📤 文档发布流程

### 1. 本地预览

#### GitBook 预览
```bash
# 安装 GitBook CLI
npm install -g gitbook-cli

# 预览文档
gitbook serve
```

#### GitHub Pages 预览
```bash
# 使用 Jekyll 本地预览
bundle exec jekyll serve
```

### 2. 提交更改

```bash
# 添加所有修改
git add .

# 提交更改
git commit -m "docs: 添加新项目 Warden 教程"

# 推送到远程仓库
git push origin main
```

### 3. 部署检查

提交后检查以下平台是否正常：

- ✅ **GitHub**: 直接在仓库中查看
- ✅ **GitBook**: 检查左侧导航和内容
- ✅ **GitHub Pages**: 检查网站是否正常访问

---

## 📋 编辑规范

### 1. 文件命名规范

- 使用小写字母
- 单词间用连字符 `-` 分隔
- 使用描述性名称
- 示例：`warden.md`、`new-project.md`

### 2. 标题规范

```markdown
# 一级标题（项目名称）
## 🎯 二级标题（使用emoji）
### 三级标题
#### 四级标题
```

### 3. 链接规范

#### 导航链接
每个页面顶部必须包含返回链接：
```markdown
[← 返回上级页面](../README.md) | [← 返回主页](../../README.md)
```

#### 内部链接
使用相对路径：
```markdown
[链接文本](../other-folder/file.md)
```

### 4. 表格规范

```markdown
| 列标题1 | 列标题2 |
|---------|---------|
| 内容1   | 内容2   |
```

### 5. 代码规范

#### 行内代码
```markdown
使用 `代码` 标记
```

#### 代码块
```markdown
```bash
# 命令示例
git add .
``` 
```

### 6. 提示框规范

```markdown
> 🎯 **提示**：重要提示内容
> ⚠️ **注意**：警告信息
> ❓ **问题**：问题描述
```

---

## ❓ 常见问题

### Q：如何快速复制项目模板？
**A：** 复制现有项目文件（如 `irys.md`），然后修改内容。记住要更新所有相关的导航文件。

### Q：添加新项目后，GitBook 左侧导航没有显示？
**A：** 检查 `SUMMARY.md` 文件是否正确添加了新项目的链接。

### Q：图片如何添加到文档中？
**A：** 建议使用外链或将图片放在 `images/` 文件夹中，然后使用相对路径引用。

### Q：如何批量更新链接？
**A：** 使用代码编辑器的"查找和替换"功能，可以批量更新相同的链接。

### Q：文档更新后多久能看到效果？
**A：** 
- GitHub：立即生效
- GitBook：通常1-2分钟
- GitHub Pages：可能需要几分钟

---

## 👥 团队协作

### 分工建议

| 角色 | 负责内容 |
|------|----------|
| 技术负责人 | 项目交互、链上工具文档 |
| 产品负责人 | 功能说明、用户指南 |
| 测试人员 | 常见问题、故障排除 |
| 运营人员 | 更新日志、版本发布 |

### 协作流程

1. **功能开发完成** → 技术负责人编写技术文档
2. **产品验收** → 产品负责人完善用户指南
3. **测试完成** → 测试人员补充常见问题
4. **版本发布** → 运营人员更新版本日志

---

## 📞 联系方式

如果在文档编辑过程中遇到问题，请联系：

- **文档维护**：[文档负责人]
- **技术支持**：@1号技师全心全意为您服务
- **产品问题**：@2号牛马在线解答

---

📝 **文档版本**：v1.0  
📅 **更新时间**：2025/7/15  
👤 **维护者**：FarmMaster 团队