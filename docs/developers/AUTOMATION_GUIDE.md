# 🤖 GitHub Actions 自动化指南

> 📋 **版本**: v1.0  
> 📅 **创建日期**: 2025/7/15  
> 👤 **维护者**: FarmMaster 团队

## 🎯 功能概述

GitHub Actions 自动化系统可以自动处理以下高频操作：

### ✅ 已实现的自动化功能

1. **版本信息自动更新** - 当 `changelog/README.md` 更新时自动同步版本信息
2. **新项目检查** - 检测新项目文件并生成导航更新提醒
3. **FAQ 统计更新** - 自动统计问题数量并更新相关文件
4. **智能通知** - 自动发送执行结果通知

---

## 🚀 触发条件

### 自动触发
当以下文件被修改并推送到 `main` 分支时：
- `changelog/README.md` → 触发版本信息更新
- `faq/README.md` → 触发 FAQ 统计更新
- `usage/**/*.md` → 触发新项目检查

### 手动触发
在 GitHub 仓库的 Actions 页面可以手动运行工作流。

---

## 📋 自动化详情

### 1. 版本信息自动更新

#### 触发条件
- `changelog/README.md` 文件被修改

#### 执行流程
1. 解析 changelog 中的最新版本信息
2. 提取版本号、日期和主要更新内容
3. 自动更新 `README.md` 中的版本信息部分
4. 自动更新 `index.md` 中的版本信息部分
5. 提交更改到主分支

#### 解析格式
脚本能够解析以下格式的 changelog：
```markdown
## 版本 v1.7.3 (2025/7/15)

### 更新内容

1. **银河（Galxe）- 修复：**
   - 恢复领取积分功能；
   - 改进打码功能；

2. **指纹浏览器 - 修复：**
   - 修复一次只能读取10个的问题；
```

#### 生成结果
更新后的版本信息格式（置顶显示）：
```markdown
## 🚀 版本信息

**当前版本**：v1.7.3 (2025/7/15)

**最新更新**：
- 银河（Galxe）- 修复 - 恢复领取积分功能；改进打码功能
- 指纹浏览器 - 修复 - 修复一次只能读取10个的问题
```

### 2. 新项目检查

#### 触发条件
- `usage/` 目录下的 `.md` 文件被修改

#### 执行流程
1. 扫描所有项目文件
2. 检查导航文件中的链接完整性
3. 生成项目统计报告
4. 如发现缺失链接，生成提醒文件

#### 检查范围
- `usage/project-interaction/*.md`
- `usage/fingerprint-browser/*.md`
- `usage/galxe/*.md`
- `usage/onchain-tools/*.md`

#### 生成文件
如果发现缺失链接，会生成 `PROJECT_UPDATE_REMINDER.md` 提醒文件。

### 3. FAQ 统计更新

#### 触发条件
- `faq/README.md` 文件被修改

#### 执行流程
1. 解析 FAQ 文件中的问题数量
2. 按类别统计问题
3. 更新主页中的 FAQ 统计信息
4. 生成统计报告

#### 更新位置
- `README.md` 中的 FAQ 链接
- `index.md` 中的 FAQ 链接

---

## 🔔 通知系统

### 成功通知
当自动化执行成功时，会在 GitHub Issues 中创建通知：
- 📊 执行统计信息
- 🔗 相关链接
- 📅 执行时间

### 失败通知
当自动化执行失败时，会创建错误报告：
- ❌ 错误信息
- 🔍 排查建议
- 📋 解决步骤

---

## 🛠️ 本地测试

### 测试单个脚本
```bash
# 测试版本信息更新
cd .github/scripts
node update-version-info.js

# 测试新项目检查
node check-new-projects.js

# 测试 FAQ 统计
node update-faq-stats.js
```

### 运行完整测试
```bash
cd .github/scripts
node test-scripts.js
```

---

## 📁 文件结构

```
.github/
├── workflows/
│   └── auto-update-docs.yml     # GitHub Actions 工作流
└── scripts/
    ├── package.json             # 脚本配置
    ├── update-version-info.js   # 版本信息更新
    ├── check-new-projects.js    # 新项目检查
    ├── update-faq-stats.js      # FAQ 统计更新
    └── test-scripts.js          # 测试脚本
```

---

## 🔧 维护指南

### 修改自动化逻辑
1. 编辑 `.github/scripts/` 目录下的对应脚本
2. 运行 `node test-scripts.js` 进行测试
3. 提交更改，自动生效

### 修改触发条件
1. 编辑 `.github/workflows/auto-update-docs.yml`
2. 修改 `paths` 配置
3. 提交更改，自动生效

### 添加新功能
1. 在 `.github/scripts/` 目录创建新脚本
2. 在工作流文件中添加调用步骤
3. 更新本文档

---

## ⚠️ 注意事项

### 权限设置
- GitHub Actions 需要有仓库的写权限
- 确保 `GITHUB_TOKEN` 具有足够权限

### 格式要求
- changelog 必须遵循指定格式
- FAQ 问题必须使用 `**问：**` 格式
- 项目文件必须在指定目录结构中

### 错误处理
- 如果解析失败，会创建错误报告
- 可以通过 GitHub Issues 查看详细错误信息
- 支持手动重新运行失败的工作流

---

## 📊 使用统计

### 预期效果
- **版本更新**: 从手动 5 分钟 → 自动 30 秒
- **新项目检查**: 从手动 10 分钟 → 自动 1 分钟
- **FAQ 统计**: 从手动 3 分钟 → 自动 10 秒

### 成功率目标
- 版本信息更新: 95%+
- 新项目检查: 90%+
- FAQ 统计: 98%+

---

## 🆘 故障排除

### 常见问题

#### 1. 版本信息解析失败
**原因**: changelog 格式不符合要求
**解决**: 检查版本标题和更新内容格式

#### 2. 自动提交失败
**原因**: 权限不足或分支保护
**解决**: 检查 GitHub Token 权限设置

#### 3. 脚本运行错误
**原因**: 文件路径或格式问题
**解决**: 运行本地测试脚本检查

### 获取帮助
- 查看 GitHub Actions 日志
- 运行本地测试脚本
- 联系技术负责人

---

## 🚀 未来计划

### 第二阶段功能
- [ ] 自动生成项目模板
- [ ] 智能链接检查
- [ ] 多语言支持

### 第三阶段功能
- [ ] 自动化部署
- [ ] 性能监控
- [ ] 用户反馈集成

---

📝 **文档更新**: 如有疑问或建议，请联系 FarmMaster 团队