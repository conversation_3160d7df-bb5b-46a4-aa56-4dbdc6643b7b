# 🧑‍💻 开发者文档

> 📋 **目标**：为 FarmMaster 团队开发者提供完整的文档维护和开发指南

## 📚 文档目录

### 🎯 快速入门
- [📋 快速参考卡片](QUICK_REFERENCE.md) - 常用操作 3 分钟上手
- [📚 团队编辑指南](TEAM_EDITING_GUIDE.md) - 详细的编辑和维护流程

### 🤖 自动化系统
- [🔧 自动化指南](AUTOMATION_GUIDE.md) - GitHub Actions 自动化完整说明

## 🚀 新手快速上手

### 1. 阅读顺序
1. **[快速参考卡片](QUICK_REFERENCE.md)** - 了解基本操作
2. **[团队编辑指南](TEAM_EDITING_GUIDE.md)** - 掌握编辑流程
3. **[自动化指南](AUTOMATION_GUIDE.md)** - 了解自动化功能

### 2. 常见任务
- **添加新项目** → 查看快速参考卡片
- **编辑现有内容** → 查看团队编辑指南
- **维护自动化** → 查看自动化指南

## 📋 文档维护分工

| 角色 | 负责文档 | 主要任务 |
|------|----------|----------|
| 开发团队 | 项目交互、链上工具 | 添加新项目文档 |
| 产品团队 | 使用指南、功能说明 | 更新功能说明 |
| 测试团队 | 常见问题、故障排除 | 补充 FAQ |
| 运营团队 | 版本日志、发布说明 | 维护更新日志 |

## 🔧 工具和资源

### 编辑工具
- **代码编辑器**: VS Code、Sublime Text 等
- **Markdown 预览**: Typora、Mark Text 等
- **Git 工具**: GitHub Desktop、GitKraken 等

### 在线资源
- **官网**: https://farmmaster.xyz/
- **Twitter**: https://x.com/FarmMaster_bot
- **GitHub**: 当前仓库

## 📞 技术支持

### 文档相关问题
- **文档编辑**: 查看团队编辑指南
- **自动化问题**: 查看自动化指南
- **格式问题**: 查看快速参考卡片

### 联系方式
- **技术负责人**: 处理复杂技术问题
- **文档维护**: 处理文档结构问题
- **自动化支持**: 处理 GitHub Actions 问题

## 🎯 最佳实践

### 文档编写
1. 使用清晰的标题结构
2. 添加必要的导航链接
3. 遵循统一的格式规范
4. 及时更新过时信息

### 团队协作
1. 分工明确，避免重复工作
2. 及时沟通，确保信息同步
3. 定期检查，保持文档质量
4. 利用自动化，提高效率

### 版本管理
1. 使用有意义的提交信息
2. 及时推送更改
3. 检查自动化执行结果
4. 处理合并冲突

## 📊 效率提升

### 自动化优势
- **版本更新**: 5 分钟 → 30 秒
- **新项目检查**: 10 分钟 → 1 分钟
- **FAQ 统计**: 3 分钟 → 10 秒
- **错误检测**: 手动 → 自动

### 建议工作流
1. 编辑内容 → 使用快速参考
2. 提交更改 → 自动化处理
3. 检查结果 → 查看通知
4. 处理问题 → 查看相关指南

---

💡 **提示**: 这些文档会随着项目发展持续更新，请定期查看最新版本。

📝 **文档版本**: v1.0  
📅 **更新时间**: 2025/7/15  
👤 **维护者**: FarmMaster 团队